<script setup lang="ts">
// 1. 导入区域
import MaterialRequestDetail from './components/MaterialRequestDetail.vue'
import MaterialRequestForm from './components/MaterialRequestForm.vue'
import { materialRequestService } from './service/MaterialRequestService'

// 2. 服务初始化
materialRequestService.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form :ref="materialRequestService.tableFormRef" v-privilege="'material-request:query'">
      <Grid>
        <GridCol>
          <FormItem label="确认单号" name="requestNo">
            <InputText allow-clear placeholder="请输入确认单号" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="SKU编码" name="skuCode">
            <InputText allow-clear placeholder="请输入SKU编码" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="SKU名称" name="skuName">
            <InputText allow-clear placeholder="请输入SKU名称" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="确认状态" name="status">
            <DictSelect dict-code="material_request_status" allow-clear placeholder="请选择状态" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="需求日期" name="requestDateFrom">
            <DatePicker allow-clear placeholder="开始日期" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="至" name="requestDateTo">
            <DatePicker allow-clear placeholder="结束日期" />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons
          add-config="material-request:add"
          batch-delete-config="material-request:delete"
          import-config="material-request:import"
          export-config="material-request:export"
        />
      </FlexRow>
      <ButtonGroup>
        <IconButton
          label="查询"
          icon-type="SearchOutlined"
          type="primary"
          @click="materialRequestService.onSearch"
        />
        <IconButton
          label="重置"
          icon-type="ReloadOutlined"
          @click="materialRequestService.resetQuery"
        />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable min-width="180rem">
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'material-request:view'"
              label="查看"
              icon-type="EyeOutlined"
              @click="materialRequestService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'material-request:edit'"
              label="编辑"
              icon-type="EditOutlined"
              @click="materialRequestService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'material-request:delete'"
              label="删除"
              icon-type="DeleteOutlined"
              color="red"
              @click="materialRequestService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>

  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 弹窗组件 -->
  <MaterialRequestDetail />
  <MaterialRequestForm />
</template>

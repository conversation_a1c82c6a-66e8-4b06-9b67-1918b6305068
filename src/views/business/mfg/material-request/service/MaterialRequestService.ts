import type { MaterialRequestForm, MaterialRequestPageParam, MaterialRequestResult } from '@/api/business/material-request/model/material-request-ex'
import { materialRequestApi } from '@/api/business/material-request/material-request-api'
import { TableCrudService as CrudService } from '@/service/composite/TableCrudService'
import { columns } from '../config/columns'

/**
 * 物料申请服务
 * 提供物料申请相关的业务逻辑和数据管理
 */
export class MaterialRequestService extends CrudService<MaterialRequestResult, MaterialRequestForm, MaterialRequestPageParam> {
  constructor() {
    // 初始化服务
    super(
      '物料申请', // 业务名称
      columns,
      {
        // 使用已有的API
        add: materialRequestApi.addMaterialRequest,
        queryPage: materialRequestApi.materialRequestPage,
        getDetail: materialRequestApi.materialRequestDetail,
        update: materialRequestApi.updateMaterialRequest,
        import: materialRequestApi.importMaterialRequest,
        export: materialRequestApi.exportMaterialRequest,
        delete: materialRequestApi.deleteMaterialRequest,
        batchDelete: materialRequestApi.batchDeleteMaterialRequest,
      }
    )
  }
}

// 单例模式
export const materialRequestService = new MaterialRequestService()

<script setup lang="ts">
import type { MaterialRequestService } from '../service/MaterialRequestService'
import { CRUD_KEY } from '@/service/composite/TableCrudService'

// 获取服务实例
const materialRequestService = inject<MaterialRequestService>(CRUD_KEY)!

// 当前激活的标签页
const activeTab = ref('basic')

// 详情页无需错误状态计算
</script>

<template>
  <Dialog 
    v-model:visible="materialRequestService.detailOpen" 
    :header="materialRequestService.detailTitle" 
    :show-footer="false" 
    width="1200px"
  >
    <!-- 详情分页容器 -->
    <BaseTabs v-model:value="activeTab">
      <!-- 标签页导航（无错误指示） -->
      <BaseTabList>
        <BaseTab value="basic">
          📋 基本信息
        </BaseTab>
        <BaseTab value="sku">
          📦 SKU信息
        </BaseTab>
        <BaseTab value="quantity">
          📊 数量信息
        </BaseTab>
        <BaseTab value="system">
          ⚙️ 系统信息
        </BaseTab>
      </BaseTabList>

      <!-- 标签页内容 -->
      <BaseTabPanels>
        <!-- 基本信息 -->
        <BaseTabsPanel value="basic">
          <Descriptions :column="2" :colon="false" bordered>
            <DescriptionsItem label="申请ID">
              <Text :value="materialRequestService.detailData.id?.toString()" />
            </DescriptionsItem>
            <DescriptionsItem label="确认单号">
              <Text :value="materialRequestService.detailData.requestNo" />
            </DescriptionsItem>
            <DescriptionsItem label="需求日期">
              <Text :value="materialRequestService.detailData.requestDate" />
            </DescriptionsItem>
            <DescriptionsItem label="确认状态">
              <DictTag 
                :color="materialRequestService.detailData.status === 'confirmed' ? 'success' : materialRequestService.detailData.status === 'pending' ? 'warning' : 'error'"
                key-code="material_request_status" 
                :value-code="materialRequestService.detailData.status" 
              />
            </DescriptionsItem>
            <DescriptionsItem label="备注" :span="2">
              <Text :value="materialRequestService.detailData.remark" />
            </DescriptionsItem>
          </Descriptions>
        </BaseTabsPanel>

        <!-- SKU信息 -->
        <BaseTabsPanel value="sku">
          <Descriptions :column="2" :colon="false" bordered>
            <DescriptionsItem label="SKU ID">
              <Text :value="materialRequestService.detailData.skuId?.toString()" />
            </DescriptionsItem>
            <DescriptionsItem label="SKU编码">
              <Text :value="materialRequestService.detailData.skuCode" />
            </DescriptionsItem>
            <DescriptionsItem label="SKU名称" :span="2">
              <Text :value="materialRequestService.detailData.skuName" />
            </DescriptionsItem>
          </Descriptions>
        </BaseTabsPanel>

        <!-- 数量信息 -->
        <BaseTabsPanel value="quantity">
          <Descriptions :column="2" :colon="false" bordered>
            <DescriptionsItem label="需求数量">
              <Text :value="materialRequestService.detailData.requiredQuantity?.toString()" />
            </DescriptionsItem>
            <DescriptionsItem label="计量单位">
              <Text :value="materialRequestService.detailData.unit" />
            </DescriptionsItem>
          </Descriptions>
        </BaseTabsPanel>

        <!-- 系统信息 -->
        <BaseTabsPanel value="system">
          <Descriptions :column="2" :colon="false" bordered>
            <DescriptionsItem label="创建时间">
              <Text :value="formatDateTime(materialRequestService.detailData.createTime)" />
            </DescriptionsItem>
            <DescriptionsItem label="更新时间">
              <Text :value="formatDateTime(materialRequestService.detailData.updateTime)" />
            </DescriptionsItem>
            <DescriptionsItem label="创建用户ID">
              <Text :value="materialRequestService.detailData.createUserId?.toString()" />
            </DescriptionsItem>
            <DescriptionsItem label="更新用户ID">
              <Text :value="materialRequestService.detailData.updateUserId?.toString()" />
            </DescriptionsItem>
          </Descriptions>
        </BaseTabsPanel>
      </BaseTabPanels>
    </BaseTabs>
  </Dialog>
</template>

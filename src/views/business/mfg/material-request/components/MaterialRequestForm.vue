<script setup lang="ts">
import type { MaterialRequestService } from '../service/MaterialRequestService'
import { CRUD_KEY } from '@/service/composite/TableCrudService'
// 表单验证规则
import { rules } from '../config/rule'

// 获取服务实例
const materialRequestService = inject<MaterialRequestService>(CRUD_KEY)!
// 当前激活的标签页
const activeTab = ref('basic')
</script>

<template>
  <Dialog
    v-model:visible="materialRequestService.formOpen"
    :header="materialRequestService.formTitle"
  >
    <Form
      :ref="materialRequestService.formRef"
      :form-data="materialRequestService.formData"
      :rules="rules"
    >
      <!-- 标签页导航 -->
      <BaseTabs v-model:value="activeTab">
        <BaseTabList>
          <BaseTab value="basic">
            📋 基本信息
          </BaseTab>
          <BaseTab value="sku">
            📦 SKU信息
          </BaseTab>
          <BaseTab value="quantity">
            📊 数量信息
          </BaseTab>
          <BaseTab value="other">
            📝 其他信息
          </BaseTab>
        </BaseTabList>

        <BaseTabPanels>
          <!-- 基本信息标签页 -->
          <BaseTabsPanel value="basic">
            <Grid :cols="2" :responsive="false">
              <GridCol>
                <FormItem label="申请ID (自动生成)" name="id">
                  <InputNumber disabled />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="确认单号" name="requestNo">
                  <InputText placeholder="请输入确认单号" allow-clear />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="需求日期" name="requestDate">
                  <DatePicker placeholder="请选择需求日期" allow-clear />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="确认状态" name="status">
                  <DictSelect
                    dict-code="material_request_status"
                    placeholder="请选择确认状态"
                    allow-clear
                  />
                </FormItem>
              </GridCol>
            </Grid>
          </BaseTabsPanel>

          <!-- SKU信息标签页 -->
          <BaseTabsPanel value="sku">
            <Grid :cols="2" :responsive="false">
              <GridCol>
                <FormItem label="SKU ID" name="skuId">
                  <InputNumber placeholder="请输入SKU ID" allow-clear />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="SKU编码" name="skuCode">
                  <InputText placeholder="请输入SKU编码" allow-clear />
                </FormItem>
              </GridCol>
              <GridCol :span="2">
                <FormItem label="SKU名称" name="skuName">
                  <InputText placeholder="请输入SKU名称" allow-clear />
                </FormItem>
              </GridCol>
            </Grid>
          </BaseTabsPanel>

          <!-- 数量信息标签页 -->
          <BaseTabsPanel value="quantity">
            <Grid :cols="2" :responsive="false">
              <GridCol>
                <FormItem label="需求数量" name="requiredQuantity">
                  <InputNumber
                    placeholder="请输入需求数量"
                    allow-clear
                    :min="0.01"
                    :precision="4"
                  />
                </FormItem>
              </GridCol>
              <GridCol>
                <FormItem label="计量单位" name="unit">
                  <InputText placeholder="请输入计量单位" allow-clear />
                </FormItem>
              </GridCol>
            </Grid>
          </BaseTabsPanel>

          <!-- 其他信息标签页 -->
          <BaseTabsPanel value="other">
            <Grid :cols="1" :responsive="false">
              <GridCol>
                <FormItem label="备注" name="remark">
                  <InputTextarea
                    placeholder="请输入备注"
                    allow-clear
                    :rows="4"
                    :maxlength="500"
                    show-count
                  />
                </FormItem>
              </GridCol>
            </Grid>
          </BaseTabsPanel>
        </BaseTabPanels>
      </BaseTabs>
    </Form>

    <template #footer>
      <Button label="取消" severity="danger" @click="materialRequestService.closeForm()" />
      <Button label="保存" @click="materialRequestService.submitFormAndRefresh()" />
    </template>
  </Dialog>
</template>

import type { MaterialRequestResult } from '@/api/business/material-request/model/material-request-ex'
import type { DictColor } from '@/components/utils/Dict/type'
import type { TableColumn } from '@/service/base/interface/Table'
import { CustomText } from '@/components/base/CustomText'
import { DictTag } from '@/components/utils/Dict'
import { h } from 'vue'

// 获取状态对应的颜色
export function getStatusColor(status?: string): DictColor {
  switch (status) {
    case 'confirmed':
      return 'success'
    case 'pending':
      return 'warning'
    case 'rejected':
      return 'error'
    default:
      return 'default'
  }
}

// 列定义
export const columns: TableColumn<MaterialRequestResult>[] = [
  {
    title: '申请ID',
    dataIndex: 'id',
    width: 100,
    customRender: ({ record }: { record: MaterialRequestResult }) => {
      return h(CustomText, {
        value: record.id?.toString(),
        copyable: true
      })
    }
  },
  {
    title: '确认单号',
    dataIndex: 'requestNo',
    width: 150,
    customRender: ({ record }: { record: MaterialRequestResult }) => {
      return h(CustomText, {
        value: record.requestNo,
        copyable: true
      })
    }
  },
  {
    title: 'SKU编码',
    dataIndex: 'skuCode',
    width: 120,
    customRender: ({ record }: { record: MaterialRequestResult }) => {
      return h(CustomText, {
        value: record.skuCode,
        copyable: true
      })
    }
  },
  {
    title: 'SKU名称',
    dataIndex: 'skuName',
    width: 200,
    ellipsis: true,
    customRender: ({ record }: { record: MaterialRequestResult }) => {
      return h(CustomText, {
        value: record.skuName
      })
    }
  },
  {
    title: '需求数量',
    dataIndex: 'requiredQuantity',
    width: 120,
    customRender: ({ record }: { record: MaterialRequestResult }) => {
      return h(CustomText, {
        value: record.requiredQuantity?.toString()
      })
    }
  },
  {
    title: '计量单位',
    dataIndex: 'unit',
    width: 100,
    customRender: ({ record }: { record: MaterialRequestResult }) => {
      return h(CustomText, {
        value: record.unit
      })
    }
  },
  {
    title: '需求日期',
    dataIndex: 'requestDate',
    width: 120,
    sorter: true
  },
  {
    title: '确认状态',
    dataIndex: 'status',
    width: 120,
    customRender: ({ record }: { record: MaterialRequestResult }) => {
      return h(DictTag, {
        color: getStatusColor(record.status),
        keyCode: 'material_request_status',
        valueCode: record.status
      })
    }
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
    ellipsis: true,
    customRender: ({ record }: { record: MaterialRequestResult }) => {
      return h(CustomText, {
        value: record.remark
      })
    }
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    sorter: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 200
  }
]

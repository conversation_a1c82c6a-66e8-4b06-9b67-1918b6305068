import type { Rules } from '@/components/base/Form/type'

export const rules: Rules = {
  // 必填字段（basic标签页）- 只有必填字段才添加到rules中
  parentSkuId: [
    { type: 'number', required: true, message: '请选择成品SKU', trigger: 'change' }
  ],
  childSkuId: [
    { type: 'number', required: true, message: '请选择组件SKU', trigger: 'change' }
  ],
  quantity: [
    { type: 'number', required: true, message: '请输入用量数量', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '用量数量必须大于0', trigger: 'blur' }
  ],
  unit: [
    { type: 'string', required: true, message: '请输入计量单位', trigger: 'blur' },
    { max: 20, message: '计量单位长度不能超过20个字符', trigger: 'blur' }
  ],
  status: [
    { type: 'string', required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 重要说明：
// 1. 🚨 选填字段不要放入rules中（会自动变成必填项）
// 2. 只有必填字段才添加到rules，并设置required: true
// 3. id、createTime、updateTime等自动生成字段不需要验证规则
// 4. 每个字段都必须指定type属性，类型参考对应的Model定义
// 5. sortOrder、remark等为选填字段，不添加到rules中

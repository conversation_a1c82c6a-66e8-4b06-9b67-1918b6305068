import type { BomResult } from '@/api/business/bom/model/bom-ex'
import type { DictColor } from '@/components/utils/Dict/type'
import type { TableColumn } from '@/service/base/interface/Table'
import { CustomText } from '@/components/base/CustomText'
import { DictTag } from '@/components/utils/Dict'
import { h } from 'vue'

// 获取状态对应的颜色
export function getStatusColor(status?: string): DictColor {
  switch (status) {
    case '1':
      return 'success'
    case '0':
      return 'warning'
    default:
      return 'default'
  }
}

// 列定义
export const columns: TableColumn<BomResult>[] = [
  {
    title: 'BOM ID',
    dataIndex: 'id',
    width: 100,
    customRender: ({ record }: { record: BomResult }) => {
      return h(CustomText, {
        value: record.id?.toString(),
        copyable: true
      })
    }
  },
  {
    title: '成品SKU ID',
    dataIndex: 'parentSkuId',
    width: 120,
    customRender: ({ record }: { record: BomResult }) => {
      return h(CustomText, {
        value: record.parentSkuId?.toString()
      })
    }
  },
  {
    title: '组件SKU ID',
    dataIndex: 'childSkuId',
    width: 120,
    customRender: ({ record }: { record: BomResult }) => {
      return h(CustomText, {
        value: record.childSkuId?.toString()
      })
    }
  },
  {
    title: '用量数量',
    dataIndex: 'quantity',
    width: 100,
    customRender: ({ record }: { record: BomResult }) => {
      return h(CustomText, {
        value: record.quantity?.toString()
      })
    }
  },
  {
    title: '计量单位',
    dataIndex: 'unit',
    width: 100,
    customRender: ({ record }: { record: BomResult }) => {
      return h(CustomText, {
        value: record.unit
      })
    }
  },
  {
    title: '排序号',
    dataIndex: 'sortOrder',
    width: 100,
    customRender: ({ record }: { record: BomResult }) => {
      return h(CustomText, {
        value: record.sortOrder?.toString()
      })
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ record }: { record: BomResult }) => {
      return h(DictTag, {
        color: getStatusColor(record.status),
        keyCode: 'bom_status',
        valueCode: record.status
      })
    }
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
    ellipsis: true,
    customRender: ({ record }: { record: BomResult }) => {
      return h(CustomText, {
        value: record.remark
      })
    }
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    sorter: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 200
  }
]

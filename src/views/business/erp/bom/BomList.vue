<script setup lang="ts">
// 1. 导入区域
import BomDetail from './components/BomDetail.vue'
import BomForm from './components/BomForm.vue'
import { bomService } from './service/BomService'

// 2. 服务初始化
bomService.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form :ref="bomService.tableFormRef" v-privilege="'bom:query'">
      <Grid>
        <GridCol>
          <FormItem label="成品SKU ID" name="parentSkuId">
            <InputNumber allow-clear placeholder="请输入成品SKU ID" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="组件SKU ID" name="childSkuId">
            <InputNumber allow-clear placeholder="请输入组件SKU ID" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="计量单位" name="unit">
            <InputText allow-clear placeholder="请输入计量单位" />
          </FormItem>
        </GridCol>
        <GridCol>
          <FormItem label="状态" name="status">
            <DictSelect dict-code="bom_status" allow-clear placeholder="请选择状态" />
          </FormItem>
        </GridCol>
      </Grid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons 
          add-config="bom:add" 
          batch-delete-config="bom:delete" 
          import-config="bom:import" 
          export-config="bom:export" 
        />
      </FlexRow>
      <ButtonGroup>
        <IconButton 
          label="查询" 
          icon-type="SearchOutlined" 
          type="primary" 
          @click="bomService.onSearch" 
        />
        <IconButton 
          label="重置" 
          icon-type="ReloadOutlined" 
          @click="bomService.resetQuery" 
        />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <AxTable min-width="180rem">
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'bom:view'" 
              label="查看" 
              icon-type="EyeOutlined"
              @click="bomService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'bom:edit'" 
              label="编辑" 
              icon-type="EditOutlined"
              @click="bomService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'bom:delete'" 
              label="删除" 
              icon-type="DeleteOutlined" 
              color="red"
              @click="bomService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </AxTable>
  </Card>

  <!-- 导入弹窗 -->
  <AxImportExcelModal />
  <!-- 弹窗组件 -->
  <BomDetail />
  <BomForm />
</template>
